<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Laravel\Scout\Searchable;

class PlaceMap extends Model
{

    use HasFactory, Searchable;

    protected $table = 'PlaceMap';

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'image',
        'key',
        'visibility',
        'customFields',
        'status',
        'zoom',
        'type'
    ];

    protected $casts = [
        'customFields' => 'array',
    ];

    public function toSearchableArray(): array
    {
        return array_merge($this->toArray(), [
            'id' => (string) $this->id,
            'name' => $this->name,
            'description' => (string) $this->description,
            'user_id' => (string) $this->user_id,
            "created_at" => $this->created_at->timestamp,

        ]);
    }

    public function placeMapItems(): HasMany
    {
        return $this->hasMany(PlaceMapItem::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
