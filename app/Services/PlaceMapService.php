<?php

namespace App\Services;

use App\Data\CustomDataDTO;
use App\Data\GeoFancingData;
use App\Data\GpsDataDTO;
use App\Data\PlaceMapData;
use App\Data\PlaceMapItemData;
use App\Enums\MapTypeEnums;
use App\Enums\PlaceMapItemTypeEnums;
use App\Exceptions\Forbidden;
use App\Exceptions\NotFound;
use App\Models\Cell;
use App\Models\District;
use App\Models\PlaceMap;
use App\Models\PlaceMapItem;
use App\Models\Province;
use App\Models\Sector;
use App\Models\Village;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PlaceMapService
{
    public function __construct(
        protected DataMapService $dataMapService,
        protected GeoFencingService $geoFencingService
    ) {}
    public function createPlace(int $userId, PlaceMapData $data)
    {

        $key = $this->generateKey();

        PlaceMap::create([
            'user_id' => $userId,
            'type' => $data->type,
            'name' => $data->name,
            'image' => $data->image,
            'description' => $data->description,
            'template_id' => $data->templateID,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'zoom' => $data->zoom,
            'customFields' => json_encode($data->customFields),
            'key' => $key,
        ]);
    }

    public function updatePlace(int $userId, int $placeMapId, PlaceMapData $data)
    {

        $placemap = $this->validatePlaceMap($userId, $placeMapId);

        $placemap->update([
            'name' => $data->name,
            'image' => $data->image,
            'description' => $data->description,
            'template_id' => $data->templateID,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'zoom' => $data->zoom,
            'is_template' => $data->isTemplate,
            'customFields' => json_encode($data->customFields)
        ]);
    }

    public function getPlaceMapById(int $userId, int $placeMapId)
    {

        $placemap = PlaceMap::where('id', $placeMapId)->where('user_id', $userId)->first();

        throw_if(is_null($placemap), NotFound::class, 'Place map not found');

        return $placemap;
    }

    public function getMapPlace(int $userId, int $perPage, int $page, ?string $searchQuery): LengthAwarePaginator
    {

        if (is_null($searchQuery)) {

            return PlaceMap::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->paginate(perPage: $perPage, page: $page);
        }

        return PlaceMap::search($searchQuery)
            ->query(function ($query) use ($userId) {
                $query->where('user_id', $userId)
                    ->orderBy('created_at', 'desc');
            })
            ->paginate(perPage: $perPage, page: $page);
    }

    public function getSharedMap(string $mapKey)
    {

        $map =  PlaceMap::where('key', $mapKey)
            ->where('visibility', 'public')
            ->select(['id', 'name', 'image', 'description', 'visibility', 'status', 'zoom', 'customFields'])
            ->with('placeMapItems:id,name,image,description,latitude,longitude,type,address,customFields,visibility,status,place_map_id')
            ->first();

        throw_if(is_null($map), NotFound::class, 'Map not found');

        return $map;
    }

    public function createPlaceMapItem(int $userId, int $placeMapId, PlaceMapItemData $data)
    {
        $mapPlace = $this->validatePlaceMap($userId, $placeMapId);

        switch ($mapPlace->type) {
            case MapTypeEnums::ITINERARY->value:
                $placeType = [ PlaceMapItemTypeEnums::ITINERARY->value, PlaceMapItemTypeEnums::PLACE->value];
                throw_if(!in_array($data->type, $placeType), Forbidden::class, 'Invalid type');
                break;
            case MapTypeEnums::TRACKING->value:
                throw_if($data->type !== PlaceMapItemTypeEnums::MOVINGITEM->value, Forbidden::class, 'Invalid type');
                $apikey = $this->generateKey();
                $customData = [
                    'apiKey' => [
                        'key' => $apikey,
                        'isActive' => 'no',
                    ],
                    'webHook' => [
                        'url' => '',
                        'isActive' => 'no',
                    ],
                    'geoFancing' => [
                        [
                            'name' => 'ID',
                            'value' => ''
                        ],
                        [
                            'name' => 'ID-Type',
                            'value' => ''
                        ],
                        [
                            'name' => 'Action-Trigger',
                            'value' => ''
                        ],
                        [
                            'name' => 'Type',
                            'value' => ''
                        ],
                        [
                            'name' => 'Radius',
                            'value' => ''
                        ]
                    ]
                ];
                break;
            case MapTypeEnums::ADMINISTRATION->value:
                $dataArray = ['province', 'district', 'sector', 'cell', 'village','place'];
                throw_if(!in_array($data->type, $dataArray), Forbidden::class, 'Invalid type');
                break;
        }

        $customField = $mapPlace->customFields;
        $validationErrors = $this->dataMapService->validateCustomData($customField, $data->dataItems);
        if (!empty($validationErrors)) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validationErrors));
        }

        PlaceMapItem::create([
            'place_map_id' => $placeMapId,
            'name' => $data->name,
            'address' => $data->address,
            'location_id' => $data->locationID,
            'description' => $data->description,
            'image' => $data->image,
            'latitude' => $data->latitude,
            'longitude' => $data->longitude,
            'type' => $data->type,
            'source' => $data->source,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'geoFancing' => json_encode($customData ?? null),
            'dataItems' => json_encode($data->dataItems ?? null)
        ]);
    }

    public function updatePlaceMapItem(int $userId, int $placeMapId, int $placeMapItemId, PlaceMapItemData $data)
    {
        $this->validatePlaceMap($userId, $placeMapId);

        $placeMapItem = $this->validatePlaceMapItem($userId, $placeMapId, $placeMapItemId);

        $placeMapItem->update([
            'name' => $data->name,
            'address' => $data->address,
            'location_id' => $data->locationID,
            'description' => $data->description,
            'image' => $data->image,
            'latitude' => $data->latitude,
            'longitude' => $data->longitude,
            'source' => $data->source,
            'visibility' => $data->visibility,
            'status' => $data->status,
            'dataItems' => json_encode($data->dataItems ?? null)
        ]);
    }

    public function getPlaceMapItemById(int $userId, int $placeMapId, int $placeMapItemId)
    {
        $this->validatePlaceMap($userId, $placeMapId);

        $placeMapItem = $this->validatePlaceMapItem($userId, $placeMapId, $placeMapItemId);

        return $placeMapItem;
    }

    public function getPlaceMapItem(int $userId, int $placeMapId, int $perPage, int $page, ?string $searchQuery): LengthAwarePaginator
    {

        $this->validatePlaceMap($userId, $placeMapId);

        if (is_null($searchQuery)) {

            return PlaceMapItem::where('place_map_id', $placeMapId)
                ->orderBy('created_at', 'desc')
                ->paginate(perPage: $perPage, page: $page);
        }

        return PlaceMapItem::search($searchQuery)
            ->where('place_map_id', $placeMapId)
            ->orderBy('created_at', 'desc')
            ->paginate(perPage: $perPage, page: $page);
    }

    private function generateKey(): string
    {
        $timestamp = Carbon::now()->timestamp;
        $timePart = substr($timestamp, -5);

        return strtoupper(implode('-', [
            Str::random(5),
            Str::random(5),
            Str::random(5),
            $timePart,
            Str::random(5),
            Str::random(5),
        ]));
    }

    private function validatePlaceMap($userId, $placeMapId): PlaceMap
    {
        $placeMap = PlaceMap::where('id', $placeMapId)->where('user_id', $userId)->first();

        throw_if(is_null($placeMap), NotFound::class, 'Place map not found');

        return $placeMap;
    }

    private function validatePlaceMapItem($userId, $placeMapId, $placeMapItemId): PlaceMapItem
    {
        $placeMapItem = PlaceMapItem::where('id', $placeMapItemId)
            ->where('place_map_id', $placeMapId)
            ->whereHas('placeMap', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->first();

        throw_if(is_null($placeMapItem), NotFound::class, 'Place map item not found');

        return $placeMapItem;
    }

    public function updateGeoFancing(int $userId, GeoFancingData $data)
    {
        $placeMapItem = $this->validatePlaceMapItem($userId, (int)$data->placeMapId, (int)$data->placeMapItemId);

        $geoFancingSettings = json_decode($placeMapItem->geoFancing, true) ?? [];
        $geoFancingSettings['apiKey']['key'] = $placeMapItem->geoFancing['apiKey']['key'] ?? '';

        $geoFancingSettings['apiKey']['isActive'] = $data->apiKey;
        $geoFancingSettings['webHook']['isActive'] = $data->webHookStatus;
        $geoFancingSettings['webHook']['url'] = $data->webHookStatus === 'yes' ? $data->webHookUrl : '';

        if ($data->geoFancing === 'yes') {
            $this->validateGeoFancingPayload($data->geoFancingData);
            $geoFancingSettings['geoFancing'] = $data->geoFancingData;
        } else {
            $geoFancingSettings['geoFancing'] = [];
        }

        $placeMapItem->update(['geoFancing' => json_encode($geoFancingSettings)]);

    }

    public function storeGpsData(GpsDataDTO $data)
    {

        $placeMapItem = PlaceMapItem::whereNotNull('geoFancing')
            ->get()
            ->filter(function ($item) use ($data) {
                $geoFancing = json_decode($item->geoFancing, true);
                return isset($geoFancing['apiKey']['key']) && $geoFancing['apiKey']['key'] === $data->key && $geoFancing['apiKey']['isActive'] === 'yes';
            })
            ->first();

        throw_if(is_null($placeMapItem), NotFound::class, 'Invalid key');

        $placeMap = $placeMapItem->placeMap;
        $dataMap = \App\Models\DataMap::firstOrCreate(
            [
                'place_map_id' => $placeMap->id,
                'name' => 'GPS Tracking Data'
            ],
            [
                'user_id' => $placeMap->user_id,
                'description' => 'Real-time GPS tracking data',
                'visibility' => 'private',
                'status' => 'active',
                'customFields' => config('geo-data.gps-field')
            ]
        );

        $dataMapItem = \App\Models\DataMapItem::firstOrCreate(
            [
                'data_map_id' => $dataMap->id,
                'place_map_item_id' => $placeMapItem->id,
            ],
            [
                'name' => $placeMapItem->name,
                'description' => 'GPS Data for ' . $placeMapItem->name,
                'visibility' => 'private',
                'status' => 'active',
                'type' => 'gps_data'
            ]
        );

        $customData = CustomDataDTO::from([
            'dataMapID' => $dataMap->id,
            'dataMapItemID' => $dataMapItem->id,
            'dataItems' => [
                ['name' => 'Address', 'value' => ''],
                ['name' => 'Timestamp', 'value' => $data->timestamp],
                ['name' => 'Latitude', 'value' => $data->latitude],
                ['name' => 'Longitude', 'value' => $data->longitude],
                ['name' => 'Details', 'value' => $data->details],
            ]
            ]);

        $this->dataMapService->createCustomData($placeMap->user_id, $customData);

        // Check geofencing after storing GPS data
        $this->geoFencingService->checkGeofencing(
            $placeMapItem,
            (float) $data->latitude,
            (float) $data->longitude,
            $data->details
        );
    }

    private function validateGeoFancingPayload(?array $geoFancingData): void
    {
        if (is_null($geoFancingData)) {
            throw new Forbidden('Geo-fencing data cannot be null when geo-fencing is enabled.');
        }

        $data = collect($geoFancingData)->keyBy('name')->map(fn($item) => $item['value']);

        \Illuminate\Support\Facades\Validator::make($data->all(), [
            'ID' => 'nullable|string|max:36',
            'ID-Type' => 'nullable|string|in:province,district,sector,cell,village',
            'Action-Trigger' => 'nullable|string',
            'Type' => 'nullable|string',
            'Radius' => 'nullable|numeric',
        ])->validate();

        $id = $data->get('ID');
        $idType = $data->get('ID-Type');

        if (empty($id) || empty($idType)) {
            return;
        }

        $modelClass = match (strtolower($idType)) {
            'province' => Province::class,
            'district' => District::class,
            'sector' => Sector::class,
            'cell' => Cell::class,
            'village' => Village::class,
            default => throw new Forbidden("Invalid ID-Type: {$idType}"),
        };

        if (!app($modelClass)->where('id', $id)->exists()) {
            throw new NotFound(class_basename($modelClass) . " not found with ID: {$id}");
        }
    }

    public function getNotifications(int $userId): array
    {
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return [];
        }

        return $user->notifications()
            ->where('type', \App\Notifications\GeoFenceNotification::class)
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => 'geofence',
                    'data' => $notification->data,
                    'read_at' => $notification->read_at,
                    'created_at' => $notification->created_at,
                ];
            })
            ->toArray();
    }
}
