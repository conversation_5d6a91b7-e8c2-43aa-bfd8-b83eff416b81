<script setup>
import AppLayout from "@/Layouts/AppLayout.vue";
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import { ref, onMounted, computed, defineComponent, h, watch, nextTick } from "vue";
import axios from "axios";
import { debounce } from "lodash";
import maplibregl from "maplibre-gl";
import "maplibre-gl/dist/maplibre-gl.css";

// --- Icon Component ---
const MapIcon = defineComponent({
    props: ["name", "size", "class"],
    setup(props) {
        const size = props.size || 20;
        const iconPaths = {
            pin: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/><circle cx="12" cy="9" r="2.5"/>`,
            star: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
            heart: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
            flag: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
            home: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
            work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
            cafe: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="currentColor" stroke-width="2" fill="none"/>`,
            park: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22v-6m0 0l-3-3m3 3l3-3m-3-10v10m0-10l-3-3m3 3l3-3M12 2v10"/><path d="M5 12h14" stroke="currentColor" stroke-width="2"/>`,
            restaurant: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
            shopping: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
            hospital: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"/><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            school: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
            plus: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v14m-7-7h14"/>`,
            close: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 6L6 18M6 6l12 12"/>`,
            search: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>`,
            lock: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 0 0-8 0v4h8v-4z"/><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            unlock: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11V7a5 5 0 0 1 9.9-1"/><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            edit: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>`,
            map: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 6v16l7-4 8 4 7-4V2l-7 4-8-4-7 4z"/>`,
            location: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>`,
        };
        return () => h("svg", { class: props.class, width: size, height: size, viewBox: "0 0 24 24", fill: "none", innerHTML: iconPaths[props.name] || iconPaths.pin });
    },
});

// --- Props ---
const props = defineProps({
    placeMapId: Number,
    geoFancingfields: Array,
});

// --- State ---
const placeMap = ref(null);
const alert = ref({ show: false, type: "", message: "" });
const placeMapItems = ref([]);
const itemsPagination = ref(null);
const itemsPerPage = ref(10);
const itemsPerPageOptions = ref([5, 10, 25, 50]);
const loading = ref({ details: true, items: true, map: false });
const searchQuery = ref("");
const selectedItem = ref(null);

// --- Map Types Configuration ---
const MAP_TYPES = {
    ITINERARY: 'itinerary',
    ADMINISTRATION: 'administration',
    GENERAL: 'general',
    TRACKING: 'tracking'
};

const getInitialItemForm = () => ({
    name: "", description: "", address: "", latitude: "", longitude: "",
    image: "pin", type: "place", locationID: null, visibility: "private",
    status: "active", dataItems: [], processing: false, errors: {},
});

const editorState = ref({
    isOpen: false,
    isEditing: false,
    itemId: null,
    form: getInitialItemForm(),
});

const mapIcons = ref([
    { name: "pin", label: "Location Pin" }, { name: "star", label: "Star" },
    { name: "heart", label: "Heart" }, { name: "flag", label: "Flag" },
    { name: "home", label: "Home" }, { name: "work", label: "Work" },
    { name: "cafe", label: "Cafe" }, { name: "park", label: "Park" },
    { name: "restaurant", label: "Restaurant" }, { name: "shopping", label: "Shopping" },
    { name: "hospital", label: "Hospital" }, { name: "school", label: "School" },
]);

// --- Map and Search State ---
const locationSearchQuery = ref("");
const locationSearchResults = ref(null);
const isSearching = ref(false);
const mapContainer = ref(null);
const map = ref(null);
const markers = ref([]);
const showMapView = ref(true); // Always show map initially
const showMapToggle = ref(true); // User can toggle map visibility

// --- Filter State for Administration Maps ---
const showFilters = ref(false);
const searchFilters = ref({
    type: 'all', // all, province, district, sector, cell, village, healthFac
    language: 'en' // en, rw, fr
});

// --- Geofencing State ---
const showGeofencing = ref(false);
const geofencingForm = ref({
    apiKey: 'no',
    webHookStatus: 'no',
    webHookUrl: '',
    geoFancing: 'no',
    geoFancingData: [],
    processing: false,
    errors: {}
});

// --- Geofencing Location Search ---
const geofencingLocationQuery = ref("");
const geofencingLocationResults = ref(null);
const isGeofencingSearching = ref(false);
const selectedGeofencingLocation = ref(null);

// --- Filter Configuration ---
const FILTER_TYPES = [
    { code: 'all', name: 'All Types', icon: 'M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z' },
    { code: 'province', name: 'Province', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z' },
    { code: 'district', name: 'District', icon: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z' },
    { code: 'sector', name: 'Sector', icon: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' },
    { code: 'cell', name: 'Cell', icon: 'M21 16V8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4A2 2 0 003 8v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4A2 2 0 0021 16z' },
    { code: 'village', name: 'Village', icon: 'M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z' },
    { code: 'healthFac', name: 'Health Facility', icon: 'M22 12h-4l-3 9L9 3l-3 9H2' }
];

const FILTER_LANGUAGES = [
    { code: 'en', name: 'English' },
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'fr', name: 'Français' }
];

// --- Computed Properties ---
const customFields = computed(() => {
    if (placeMap.value && placeMap.value.customFields) {
        try {
            const fields = typeof placeMap.value.customFields === "string" ? JSON.parse(placeMap.value.customFields) : placeMap.value.customFields;
            return Array.isArray(fields) ? fields : [];
        } catch (e) {
            console.error("Error parsing custom fields:", e);
            return [];
        }
    }
    return [];
});

const formattedSearchResults = computed(() => {
    if (!locationSearchResults.value) return [];
    const { provinces, districts, sectors, cells, villages, healthFacs } = locationSearchResults.value;
    const results = [];
    const addResults = (items, type) => items?.forEach(item => results.push({ ...item, type }));
    addResults(provinces, "Province"); addResults(districts, "District");
    addResults(sectors, "Sector"); addResults(cells, "Cell");
    addResults(villages, "Village"); addResults(healthFacs, "Health Facility");
    return results;
});

// --- Map Type Logic ---
const isAdministrationType = computed(() => placeMap.value?.type === MAP_TYPES.ADMINISTRATION);
const isTrackingType = computed(() => placeMap.value?.type === MAP_TYPES.TRACKING);
const isGeneralOrItineraryType = computed(() =>
    placeMap.value?.type === MAP_TYPES.GENERAL || placeMap.value?.type === MAP_TYPES.ITINERARY
);

const shouldShowLocationSearch = computed(() => {
    return isAdministrationType.value && !editorState.value.isEditing;
});

const shouldShowCoordinateInputs = computed(() => {
    return isGeneralOrItineraryType.value || isTrackingType.value;
});

const allowCoordinateEditing = computed(() => {
    return !isAdministrationType.value;
});

// --- Geofencing Logic ---
const canUseGeofencing = computed(() => {
    return isTrackingType.value &&
           selectedItem.value &&
           selectedItem.value.type === 'movingItem' &&
           editorState.value.isEditing;
});

const hasExistingGeofencing = computed(() => {
    if (!selectedItem.value?.geoFancing) return false;

    let geoFancingData;
    if (typeof selectedItem.value.geoFancing === 'string') {
        try {
            geoFancingData = JSON.parse(selectedItem.value.geoFancing);
        } catch (e) {
            return false;
        }
    } else {
        geoFancingData = selectedItem.value.geoFancing;
    }

    return geoFancingData &&
           (geoFancingData.apiKey?.isActive === 'yes' ||
            geoFancingData.webHook?.isActive === 'yes' ||
            (geoFancingData.geoFancing && geoFancingData.geoFancing.length > 0));
});

const getApiKey = computed(() => {
    if (!selectedItem.value?.geoFancing) return '';

    let geoFancingData;
    if (typeof selectedItem.value.geoFancing === 'string') {
        try {
            geoFancingData = JSON.parse(selectedItem.value.geoFancing);
        } catch (e) {
            return '';
        }
    } else {
        geoFancingData = selectedItem.value.geoFancing;
    }

    return geoFancingData?.apiKey?.key || '';
});

// --- Functions ---
const showAlert = (type, message, duration = 4000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => (alert.value.show = false), duration);
};

const fetchPlaceMapDetails = async () => {
    loading.value.details = true;
    try {
        const response = await axios.get(route("placeMap.getById", { placeMapId: props.placeMapId }));
        placeMap.value = response.data;
        // Initialize map view after getting place map details
        if (placeMap.value) {
            showMapView.value = true;
            nextTick(() => initMap());
        }
    } catch (error) {
        showAlert("error", "Error fetching map details.");
    } finally {
        loading.value.details = false;
    }
};

const fetchPlaceMapItems = async (page = 1, perPage = itemsPerPage.value) => {
    loading.value.items = true;
    try {
        const response = await axios.get(route("placeMap.getPlaceMapItem", {
            placeMapId: props.placeMapId,
            page,
            perPage,
            searchQuery: searchQuery.value
        }));
        placeMapItems.value = response.data.data;
        itemsPagination.value = response.data;
        // Update map markers when items are loaded
        updateMapMarkers();
    } catch (error) {
        showAlert("error", "Error fetching map items.");
    } finally {
        loading.value.items = false;
    }
};

const openEditorForCreate = () => {
    locationSearchQuery.value = "";
    locationSearchResults.value = null;

    // Set initial type based on map type
    let initialType = "place";
    if (isTrackingType.value) {
        initialType = "movingItem";
    } else if (isAdministrationType.value) {
        initialType = "village"; // Default to village for administration
    }

    editorState.value = {
        isOpen: true,
        isEditing: false,
        itemId: null,
        form: {
            ...getInitialItemForm(),
            type: initialType,
            dataItems: customFields.value.map(f => ({ name: f.name, value: "" })),
        },
    };
};

const openEditorForEdit = (item) => {
    selectedItem.value = item;
    locationSearchQuery.value = "";
    locationSearchResults.value = null;
    const existingDataItems = item.dataItems ? JSON.parse(item.dataItems) : [];
    const existingDataMap = existingDataItems.reduce((acc, curr) => ({ ...acc, [curr.name]: curr.value }), {});

    editorState.value = {
        isOpen: true,
        isEditing: true,
        itemId: item.id,
        form: {
            name: item.name || "",
            description: item.description || "",
            address: item.address || "",
            latitude: item.latitude || "",
            longitude: item.longitude || "",
            image: item.image || "pin",
            type: item.type || "place",
            locationID: item.location_id || null,
            visibility: item.visibility || "private",
            status: item.status || "active",
            dataItems: customFields.value.map(f => ({ name: f.name, value: existingDataMap[f.name] || "" })),
            processing: false,
            errors: {},
        },
    };

    // Focus on the item in the map
    if (item.latitude && item.longitude && map.value) {
        map.value.flyTo({
            center: [parseFloat(item.longitude), parseFloat(item.latitude)],
            zoom: 15,
            duration: 1000
        });
        highlightMarker(item.id);
    }
};

const closeEditor = () => {
    editorState.value.isOpen = false;
    selectedItem.value = null;
    clearHighlightedMarker();
};

const submitPlaceMapItem = async () => {
    const form = editorState.value.form;
    form.processing = true;
    form.errors = {};
    
    const url = editorState.value.isEditing
        ? route("placeMap.updatePlaceItem", { placeMapId: props.placeMapId, placeMapItemId: editorState.value.itemId })
        : route("placeMap.createPlaceItem", { placeMapId: props.placeMapId });

    try {
        const response = await axios.post(url, form);
        showAlert("success", response.data.message);
        await fetchPlaceMapItems(itemsPagination.value?.currentPage || 1, itemsPerPage.value);
        if (!editorState.value.isEditing) {
            closeEditor();
        }
    } catch (error) {
        const errorMessage = error.response?.data?.message || "An unexpected error occurred.";
        showAlert("error", errorMessage);
        if (error.response?.status === 422) {
            form.errors = error.response.data.errors;
        }
    } finally {
        form.processing = false;
    }
};

// --- Map Functions ---
const initMap = () => {
    if (map.value || !mapContainer.value) return;

    const initialCenter = [29.8739, -1.9403];
    const initialZoom = 8;

    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: {
            version: 8,
            sources: {
                osm: {
                    type: 'raster',
                    tiles: ['https://a.tile.openstreetmap.org/{z}/{x}/{y}.png'],
                    tileSize: 256,
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                }
            },
            layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: {} }]
        },
        center: initialCenter,
        zoom: initialZoom
    });

    map.value.on('load', () => {
        updateMapMarkers();
    });

    map.value.on('click', (e) => {
        if (editorState.value.isOpen && allowCoordinateEditing.value) {
            const { lng, lat } = e.lngLat;
            editorState.value.form.latitude = lat.toFixed(6);
            editorState.value.form.longitude = lng.toFixed(6);
            // Auto-fill address
            reverseGeocode(lat, lng);
        } else {
            // Click on map to select item
            const features = map.value.queryRenderedFeatures(e.point);
            if (features.length > 0) {
                const feature = features[0];
                if (feature.properties && feature.properties.itemId) {
                    const item = placeMapItems.value.find(i => i.id == feature.properties.itemId);
                    if (item) {
                        openEditorForEdit(item);
                    }
                }
            }
        }
    });
};

const updateMapMarkers = () => {
    if (!map.value) return;

    // Clear existing markers
    markers.value.forEach(marker => marker.remove());
    markers.value = [];

    // Add markers for all items
    placeMapItems.value.forEach(item => {
        if (item.latitude && item.longitude) {
            const marker = createMarker(item);
            markers.value.push(marker);
        }
    });
};

const createMarker = (item) => {
    const el = document.createElement('div');
    el.className = 'custom-marker';
    el.style.cssText = `
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: ${item.visibility === 'public' ? '#22c55e' : '#6b7280'};
        border: 3px solid white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        transition: all 0.2s ease;
    `;

    // Add icon based on item.image
    const iconMap = {
        pin: '📍', star: '⭐', heart: '❤️', flag: '🚩',
        home: '🏠', work: '💼', cafe: '☕', park: '🌳',
        restaurant: '🍽️', shopping: '🛍️', hospital: '🏥', school: '🏫'
    };
    el.textContent = iconMap[item.image] || '📍';

    el.addEventListener('mouseenter', () => {
        el.style.transform = 'scale(1.2)';
        el.style.zIndex = '1000';
    });

    el.addEventListener('mouseleave', () => {
        el.style.transform = 'scale(1)';
        el.style.zIndex = '1';
    });

    const marker = new maplibregl.Marker(el)
        .setLngLat([parseFloat(item.longitude), parseFloat(item.latitude)])
        .addTo(map.value);

    marker.itemId = item.id;
    return marker;
};

const highlightMarker = (itemId) => {
    markers.value.forEach(marker => {
        const el = marker.getElement();
        if (marker.itemId == itemId) {
            el.style.border = '3px solid #3b82f6';
            el.style.transform = 'scale(1.3)';
            el.style.zIndex = '1001';
        } else {
            el.style.border = '3px solid white';
            el.style.transform = 'scale(1)';
            el.style.zIndex = '1';
        }
    });
};

const clearHighlightedMarker = () => {
    markers.value.forEach(marker => {
        const el = marker.getElement();
        el.style.border = '3px solid white';
        el.style.transform = 'scale(1)';
        el.style.zIndex = '1';
    });
};

const reverseGeocode = async (lat, lng) => {
    try {
        const response = await axios.post('/map/search-latitude-langitude-json', {
            latitude: lat,
            longitude: lng,
            lang: 'en'
        });

        if (response.data && response.data.length > 0) {
            // Use the first result for address
            const location = response.data[0];
            editorState.value.form.address = location.address || `${location.name_en || location.name}, Rwanda`;
        } else {
            // Fallback to coordinates if no results
            editorState.value.form.address = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
    } catch (error) {
        console.error('Reverse geocoding failed:', error);
        // Fallback to coordinates
        editorState.value.form.address = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
};

const destroyMap = () => {
    if (map.value) {
        markers.value.forEach(marker => marker.remove());
        markers.value = [];
        map.value.remove();
        map.value = null;
    }
};



const performLocationSearch = debounce(async () => {
    if (locationSearchQuery.value.length < 3) {
        locationSearchResults.value = null;
        return;
    }
    isSearching.value = true;
    try {
        const response = await axios.get("/api/search", {
            params: {
                filterData: searchFilters.value.type === 'all' ? editorState.value.form.type : searchFilters.value.type,
                searchQuery: locationSearchQuery.value,
                lang: searchFilters.value.language,
            },
        });
        locationSearchResults.value = response.data;
    } catch (error) {
        showAlert("error", "Failed to search for locations.");
        locationSearchResults.value = null;
    } finally {
        isSearching.value = false;
    }
}, 300);

const toggleFilters = () => {
    showFilters.value = !showFilters.value;
};

const resetFilters = () => {
    searchFilters.value.type = 'all';
    searchFilters.value.language = 'en';
    if (locationSearchQuery.value.length >= 3) {
        performLocationSearch();
    }
};

// --- Geofencing Functions ---
const initializeGeofencingForm = () => {
    if (!selectedItem.value) return;

    let geoFancingData = null;

    // Handle both string and object formats
    if (selectedItem.value.geoFancing) {
        if (typeof selectedItem.value.geoFancing === 'string') {
            try {
                geoFancingData = JSON.parse(selectedItem.value.geoFancing);
            } catch (e) {
                console.error('Failed to parse geoFancing data:', e);
                geoFancingData = null;
            }
        } else {
            geoFancingData = selectedItem.value.geoFancing;
        }
    }

    if (geoFancingData) {
        // Parse existing geofencing data
        geofencingForm.value = {
            apiKey: geoFancingData.apiKey?.isActive || 'no',
            webHookStatus: geoFancingData.webHook?.isActive || 'no',
            webHookUrl: geoFancingData.webHook?.url || '',
            geoFancing: (geoFancingData.geoFancing && geoFancingData.geoFancing.length > 0) ? 'yes' : 'no',
            geoFancingData: geoFancingData.geoFancing || props.geoFancingfields?.map(field => ({
                name: field.name,
                value: ''
            })) || [],
            processing: false,
            errors: {}
        };
    } else {
        // Initialize with default values from config (when geoFancing is null)
        geofencingForm.value = {
            apiKey: 'no',
            webHookStatus: 'no',
            webHookUrl: '',
            geoFancing: 'no',
            geoFancingData: props.geoFancingfields?.map(field => ({
                name: field.name,
                value: ''
            })) || [],
            processing: false,
            errors: {}
        };
    }
};

const openGeofencing = () => {
    if (!canUseGeofencing.value) return;
    initializeGeofencingForm();
    showGeofencing.value = true;
};

const closeGeofencing = () => {
    showGeofencing.value = false;
    geofencingForm.value.errors = {};
};

const submitGeofencing = async () => {
    if (geofencingForm.value.processing) return;

    geofencingForm.value.processing = true;
    geofencingForm.value.errors = {};

    try {
        // Clean geoFancingData when geofencing is disabled
        const geoFancingData = geofencingForm.value.geoFancing === 'yes'
            ? geofencingForm.value.geoFancingData
            : [];

        await axios.post('/geo-fancing', {
            placeMapId: props.placeMapId,
            placeMapItemId: selectedItem.value.id,
            apiKey: geofencingForm.value.apiKey,
            webHookStatus: geofencingForm.value.webHookStatus,
            webHookUrl: geofencingForm.value.webHookUrl,
            geoFancing: geofencingForm.value.geoFancing,
            geoFancingData: geoFancingData
        });

        showAlert("success", "Geofencing settings updated successfully!");
        closeGeofencing();
        fetchPlaceMapItems(); // Refresh to get updated data
    } catch (error) {
        if (error.response?.status === 422) {
            geofencingForm.value.errors = error.response.data.errors || {};
        } else {
            showAlert("error", "Failed to update geofencing settings.");
        }
    } finally {
        geofencingForm.value.processing = false;
    }
};

const getFieldType = (fieldName) => {
    const fieldConfig = props.geoFancingfields?.find(f => f.name === fieldName);
    if (fieldConfig?.type === 'number' || fieldName === 'Radius') {
        return 'number';
    }
    return 'text';
};

const getFieldPlaceholder = (fieldName) => {
    const placeholders = {
        'ID': 'Auto-generated ID',
        'ID-Type': 'e.g., cell, sector, village',
        'Action-Trigger': 'e.g., send email, send SMS',
        'Type': 'e.g., when tracking is away, when item is close',
        'Radius': 'e.g., 10 (in kilometers)'
    };
    return placeholders[fieldName] || `Enter ${fieldName.toLowerCase()}`;
};

// --- Geofencing Location Search Functions ---
const performGeofencingLocationSearch = debounce(async () => {
    if (geofencingLocationQuery.value.length < 3) {
        geofencingLocationResults.value = null;
        return;
    }
    isGeofencingSearching.value = true;
    try {
        const response = await axios.get("/api/search", {
            params: {
                filterData: 'all',
                searchQuery: geofencingLocationQuery.value,
                lang: 'en',
            },
        });
        geofencingLocationResults.value = response.data;
    } catch (error) {
        showAlert("error", "Failed to search for locations.");
        geofencingLocationResults.value = null;
    } finally {
        isGeofencingSearching.value = false;
    }
}, 300);

const selectGeofencingLocation = (location) => {
    selectedGeofencingLocation.value = location;

    // Update the ID and ID-Type fields in geofencing data
    const idField = geofencingForm.value.geoFancingData.find(field => field.name === 'ID');
    const idTypeField = geofencingForm.value.geoFancingData.find(field => field.name === 'ID-Type');

    if (idField) {
        idField.value = location.id;
    }
    if (idTypeField) {
        idTypeField.value = location.type;
    }

    geofencingLocationQuery.value = location.name_en || location.name;
    geofencingLocationResults.value = null;
};

const clearGeofencingLocation = () => {
    selectedGeofencingLocation.value = null;
    geofencingLocationQuery.value = "";
    geofencingLocationResults.value = null;

    // Clear the ID and ID-Type fields
    const idField = geofencingForm.value.geoFancingData.find(field => field.name === 'ID');
    const idTypeField = geofencingForm.value.geoFancingData.find(field => field.name === 'ID-Type');

    if (idField) {
        idField.value = '';
    }
    if (idTypeField) {
        idTypeField.value = '';
    }
};

const selectLocation = (location) => {
    const form = editorState.value.form;

    // Fill basic information
    form.name = location.name_en || location.name;
    form.locationID = location.id;

    // Fill coordinates if available
    if (location.latitude && location.longitude) {
        form.latitude = parseFloat(location.latitude);
        form.longitude = parseFloat(location.longitude);
    }

    // Fill address - try multiple sources
    if (location.address) {
        form.address = location.address;
    } else {
        // Build address from available information
        const addressParts = [];
        if (location.name_en || location.name) {
            addressParts.push(location.name_en || location.name);
        }
        if (location.district_name) {
            addressParts.push(location.district_name);
        }
        if (location.province_name) {
            addressParts.push(location.province_name);
        }
        addressParts.push('Rwanda');
        form.address = addressParts.join(', ');
    }

    // Set type based on location type for administration maps
    if (isAdministrationType.value && location.type) {
        const typeMapping = {
            'Province': 'province',
            'District': 'district',
            'Sector': 'sector',
            'Cell': 'cell',
            'Village': 'village',
            'Health Facility': 'healthFac'
        };
        form.type = typeMapping[location.type] || location.type.toLowerCase();
    }

    locationSearchQuery.value = "";
    locationSearchResults.value = null;

    // If coordinates are available, update map view
    if (form.latitude && form.longitude && map.value) {
        map.value.flyTo({
            center: [parseFloat(form.longitude), parseFloat(form.latitude)],
            zoom: 15,
            duration: 1000
        });
    }
};

const clearSelectedLocation = () => {
    const form = editorState.value.form;
    form.locationID = null;
    form.name = "";
    form.address = "";

    // Only clear coordinates for administration types if they were auto-filled
    if (isAdministrationType.value) {
        form.latitude = "";
        form.longitude = "";
    }

    locationSearchQuery.value = "";
    locationSearchResults.value = null;
};

onMounted(async () => {
    await fetchPlaceMapDetails();
    await fetchPlaceMapItems();
});

watch(searchQuery, debounce(() => {
    fetchPlaceMapItems(1, itemsPerPage.value);
}, 300));

watch(itemsPerPage, () => {
    fetchPlaceMapItems(1, itemsPerPage.value);
});

watch(locationSearchQuery, () => {
    if (shouldShowLocationSearch.value && !editorState.value.form.locationID) {
        performLocationSearch();
    }
});

watch([() => searchFilters.value.type, () => searchFilters.value.language], () => {
    if (shouldShowLocationSearch.value && locationSearchQuery.value.length >= 3) {
        performLocationSearch();
    }
});

watch(geofencingLocationQuery, () => {
    performGeofencingLocationSearch();
});

watch(() => editorState.value.form.type, () => {
    if (!editorState.value.isEditing) {
        const form = editorState.value.form;
        form.latitude = "";
        form.longitude = "";
        form.locationID = null;
        form.address = "";
        locationSearchQuery.value = "";
        locationSearchResults.value = null;
    }
});

watch(
    () => [editorState.value.form.latitude, editorState.value.form.longitude],
    debounce(([newLat, newLng]) => {
        if (map.value && newLat && newLng && editorState.value.isOpen) {
            const lat = parseFloat(newLat);
            const lng = parseFloat(newLng);
            if (!isNaN(lat) && !isNaN(lng)) {
                map.value.flyTo({ center: [lng, lat], zoom: Math.max(map.value.getZoom(), 12) });
                // Auto-fill address if empty
                if (!editorState.value.form.address) {
                    reverseGeocode(lat, lng);
                }
            }
        }
    }, 500)
);

</script>

<template>
    <AppLayout :title="placeMap ? `Editing ${placeMap.name}` : 'Map Items'">
        <Head title="Map Items" />

        <!-- Alert -->
        <div v-if="alert.show" :class="`fixed top-4 right-4 z-50 p-4 rounded-2xl border-2 ${alert.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'}`">
            {{ alert.message }}
        </div>

        <div class="min-h-screen bg-gray-50">
            <div class="flex h-screen">
                <!-- Left Panel: Item List -->
                <div class="w-96 bg-white border-r-2 border-gray-100 flex flex-col">
                    <!-- Header -->
                    <div class="p-6 border-b-2 border-gray-100">
                        <div v-if="placeMap">
                            <div class="flex justify-between items-center mb-3">
                                <Link :href="route('myMap.index')" class="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors">
                                    ← Back to My Maps
                                </Link>
                                <Link :href="route('myMapData.index', placeMapId)" class="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                                    Map Data
                                </Link>
                            </div>
                            <h1 class="text-2xl font-bold text-gray-900 truncate">{{ placeMap.name }}</h1>
                            <p class="text-sm text-gray-600 mt-1 truncate">{{ placeMap.description }}</p>
                            <div class="mt-3">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-100 text-blue-700">
                                    {{ placeMap.type }} Map
                                </span>
                            </div>
                        </div>
                        <div v-else class="space-y-3">
                            <div class="h-4 bg-gray-200 rounded-xl w-1/4 animate-pulse"></div>
                            <div class="h-6 bg-gray-200 rounded-xl w-3/4 animate-pulse"></div>
                            <div class="h-4 bg-gray-200 rounded-xl w-1/2 animate-pulse"></div>
                        </div>
                    </div>

                    <!-- Controls -->
                    <div class="p-6 border-b-2 border-gray-100 space-y-4">
                        <button @click="openEditorForCreate" class="w-full inline-flex items-center justify-center px-6 py-3 bg-gray-900 text-white rounded-2xl font-bold hover:bg-gray-800 transition-colors">
                            <MapIcon name="plus" size="20" class="mr-2"/> Add New Place
                        </button>
                        <div class="relative">
                            <input v-model="searchQuery" type="text" placeholder="Search places..." class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                            <MapIcon name="search" size="20" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        </div>
                    </div>

                    <!-- Items List -->
                    <div class="flex-1 overflow-y-auto">
                        <div v-if="loading.items" class="p-6 space-y-4">
                            <div v-for="i in 8" :key="i" class="h-16 bg-gray-200 rounded-2xl animate-pulse"></div>
                        </div>
                        <div v-else-if="placeMapItems.length > 0" class="p-4">
                            <div v-for="item in placeMapItems" :key="item.id"
                                 class="mb-3 border-2 border-gray-100 rounded-2xl p-4 hover:border-blue-200 hover:bg-blue-50/30 transition-all cursor-pointer"
                                 :class="{'border-blue-300 bg-blue-50': editorState.isOpen && editorState.itemId === item.id}"
                                 @click="openEditorForEdit(item)">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white">
                                            <MapIcon :name="item.image || 'pin'" size="20" />
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <h3 class="font-bold text-gray-900 truncate">{{ item.name }}</h3>
                                            <MapIcon :name="item.visibility === 'public' ? 'unlock' : 'lock'" size="14" :class="item.visibility === 'public' ? 'text-green-600' : 'text-gray-500'" />
                                        </div>
                                        <p class="text-sm text-gray-600 truncate">{{ item.address || 'No address' }}</p>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gray-100 text-gray-700 mt-1">
                                            {{ item.type }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="text-center p-8 text-gray-500">
                            <div class="w-16 h-16 bg-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                <MapIcon name="pin" size="32" class="text-gray-400" />
                            </div>
                            <p class="font-medium">No places found.</p>
                            <p v-if="searchQuery" class="text-sm mt-1">Try a different search term.</p>
                            <p v-else class="text-sm mt-1">Click "Add New Place" to start.</p>
                        </div>
                    </div>

                    <!-- Pagination Controls -->
                    <div v-if="itemsPagination && itemsPagination.total > 0" class="p-4 border-t-2 border-gray-100">
                        <div class="flex items-center justify-between text-sm">
                            <select v-model="itemsPerPage" class="px-3 py-2 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none">
                                <option v-for="option in itemsPerPageOptions" :key="option" :value="option">{{ option }} / page</option>
                            </select>
                            <div class="text-gray-600 font-medium">
                                {{ itemsPagination.from }}-{{ itemsPagination.to }} of {{ itemsPagination.total }}
                            </div>
                            <div class="flex items-center space-x-2">
                                <button @click="fetchPlaceMapItems(itemsPagination.current_page - 1)"
                                        :disabled="itemsPagination.current_page <= 1"
                                        class="px-3 py-2 rounded-xl border-2 border-gray-200 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                    Prev
                                </button>
                                <button @click="fetchPlaceMapItems(itemsPagination.current_page + 1)"
                                        :disabled="itemsPagination.current_page >= itemsPagination.last_page"
                                        class="px-3 py-2 rounded-xl border-2 border-gray-200 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel: Map and Editor -->
                <main class="flex-1 flex flex-col">
                    <!-- Map View (Toggleable) -->
                    <div v-if="showMapToggle" class="h-1/2 border-b-2 border-gray-100">
                        <div class="h-full bg-white">
                            <div class="p-4 border-b-2 border-gray-100">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h3 class="text-lg font-bold text-gray-900">Map View</h3>
                                        <p class="text-sm text-gray-600 mt-1">Click on markers to edit items</p>
                                    </div>
                                    <button @click="showMapToggle = false" class="p-2 rounded-xl hover:bg-gray-100 transition-colors">
                                        <MapIcon name="close" size="20" class="text-gray-600" />
                                    </button>
                                </div>
                            </div>
                            <div class="p-4 h-full">
                                <div v-if="showMapView" ref="mapContainer" class="w-full h-full rounded-2xl border-2 border-gray-200"></div>
                                <div v-else class="w-full h-full rounded-2xl border-2 border-gray-200 flex items-center justify-center bg-gray-50">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                            <MapIcon name="map" size="32" class="text-gray-400" />
                                        </div>
                                        <p class="text-gray-600 font-medium">Map will load after place details are fetched</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Show Map Button (when map is hidden) -->
                    <div v-if="!showMapToggle" class="p-4 border-b-2 border-gray-100 bg-white">
                        <button @click="showMapToggle = true" class="w-full inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-2xl font-bold hover:bg-blue-700 transition-colors">
                            <MapIcon name="map" size="20" class="mr-2" />
                            Show Map
                        </button>
                    </div>

                    <!-- Editor Panel -->
                    <div :class="showMapToggle ? 'h-1/2' : 'flex-1'" class="bg-gray-50">
                        <div v-if="!editorState.isOpen" class="h-full flex items-center justify-center text-center text-gray-500">
                            <div>
                                <div class="w-20 h-20 bg-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <MapIcon name="edit" size="32" class="text-gray-400"/>
                                </div>
                                <h2 class="text-xl font-bold text-gray-900 mb-2">Select a place to edit</h2>
                                <p class="text-gray-600">Or create a new one to get started.</p>
                                <div class="mt-6">
                                    <button v-if="!showMapToggle" @click="showMapToggle = true" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-2xl font-medium hover:bg-blue-700 transition-colors">
                                        <MapIcon name="map" size="16" class="mr-2" />
                                        Show Map
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div v-else class="h-full flex flex-col">
                            <!-- Editor Header with Submit Button -->
                            <div class="bg-white border-b-2 border-gray-100 p-4 flex-shrink-0">
                                <div class="flex justify-between items-center">
                                    <h2 class="text-xl font-bold text-gray-900">{{ editorState.isEditing ? 'Edit Place' : 'New Place' }}</h2>
                                    <div class="flex items-center space-x-3">
                                        <button v-if="!showMapToggle" @click="showMapToggle = true" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-2xl font-medium hover:bg-blue-700 transition-colors">
                                            <MapIcon name="map" size="16" class="mr-2" />
                                            Show Map
                                        </button>
                                        <button @click="closeEditor" class="p-3 rounded-2xl hover:bg-gray-100 transition-colors">
                                            <MapIcon name="close" size="20" class="text-gray-600"/>
                                        </button>
                                    </div>
                                </div>
                                <!-- Submit Button Row -->
                                <div class="mt-4 flex justify-end">
                                    <button @click="submitPlaceMapItem" :disabled="editorState.form.processing"
                                            class="inline-flex items-center px-8 py-3 bg-gray-900 text-white rounded-2xl font-bold hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                        <span v-if="editorState.form.processing" class="mr-2">
                                            <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </span>
                                        {{ editorState.form.processing ? 'Saving...' : (editorState.isEditing ? 'Update Place' : 'Create Place') }}
                                    </button>
                                </div>
                            </div>

                            <!-- Editor Form -->
                            <div class="flex-1 overflow-y-auto">
                                <div class="p-6 space-y-6">
                                <!-- Main Details Card -->
                                <div class="bg-white p-6 rounded-2xl border-2 border-gray-100">
                                    <h3 class="text-lg font-bold text-gray-900 mb-4">Basic Information</h3>
                                    <div class="space-y-4">
                                        <!-- Type Selection (conditional based on map type) -->
                                        <div v-if="!isAdministrationType || editorState.isEditing">
                                            <label class="block text-sm font-bold text-gray-700 mb-2">Type</label>
                                            <select v-model="editorState.form.type"
                                                    :disabled="isAdministrationType && editorState.isEditing"
                                                    class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors disabled:bg-gray-100">
                                                <option v-if="isGeneralOrItineraryType" value="place">Place</option>
                                                <option v-if="isTrackingType" value="movingItem">Moving Item</option>
                                                <option v-if="isAdministrationType" value="province">Province</option>
                                                <option v-if="isAdministrationType" value="district">District</option>
                                                <option v-if="isAdministrationType" value="sector">Sector</option>
                                                <option v-if="isAdministrationType" value="cell">Cell</option>
                                                <option v-if="isAdministrationType" value="village">Village</option>
                                                <option v-if="isAdministrationType" value="healthFac">Health Facility</option>
                                            </select>
                                        </div>

                                        <!-- Location Search (for administration type) -->
                                        <div v-if="shouldShowLocationSearch">
                                            <div class="flex items-center justify-between mb-2">
                                                <label class="block text-sm font-bold text-gray-700">Search Location</label>
                                                <button @click="toggleFilters"
                                                        class="p-2 rounded-xl hover:bg-gray-100 transition-colors"
                                                        :class="{ 'bg-blue-50 text-blue-600': showFilters }">
                                                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" />
                                                    </svg>
                                                </button>
                                            </div>

                                            <!-- Filters Panel -->
                                            <div v-if="showFilters" class="mb-4 p-4 bg-gray-50 border-2 border-gray-200 rounded-xl space-y-4">
                                                <div class="flex justify-between items-center">
                                                    <h4 class="font-bold text-gray-900">Search Filters</h4>
                                                    <button @click="resetFilters" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                                                        Reset
                                                    </button>
                                                </div>

                                                <!-- Type Filter -->
                                                <div>
                                                    <label class="block text-xs font-bold text-gray-600 mb-2 uppercase tracking-wide">Location Type</label>
                                                    <div class="grid grid-cols-2 gap-2">
                                                        <button v-for="filterType in FILTER_TYPES" :key="filterType.code"
                                                                @click="searchFilters.type = filterType.code"
                                                                :class="`p-3 rounded-xl border-2 transition-all text-left ${searchFilters.type === filterType.code ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 hover:border-gray-300 text-gray-700'}`">
                                                            <div class="flex items-center space-x-2">
                                                                <svg class="w-4 h-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="filterType.icon" />
                                                                </svg>
                                                                <span class="text-sm font-medium">{{ filterType.name }}</span>
                                                            </div>
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Language Filter -->
                                                <div>
                                                    <label class="block text-xs font-bold text-gray-600 mb-2 uppercase tracking-wide">Language</label>
                                                    <div class="flex space-x-2">
                                                        <button v-for="lang in FILTER_LANGUAGES" :key="lang.code"
                                                                @click="searchFilters.language = lang.code"
                                                                :class="`px-4 py-2 rounded-xl border-2 transition-all ${searchFilters.language === lang.code ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 hover:border-gray-300 text-gray-700'}`">
                                                            <span class="text-sm font-medium">{{ lang.name }}</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-if="editorState.form.locationID" class="p-4 bg-blue-50 border-2 border-blue-200 rounded-xl">
                                                <div class="flex justify-between items-center">
                                                    <div>
                                                        <div class="font-bold text-blue-900">{{ editorState.form.name }}</div>
                                                        <div class="text-sm text-blue-700">{{ editorState.form.address }}</div>
                                                    </div>
                                                    <button @click="clearSelectedLocation" class="text-blue-600 hover:text-blue-800 p-2 rounded-xl hover:bg-blue-100 transition-colors">
                                                        <MapIcon name="close" size="16" />
                                                    </button>
                                                </div>
                                            </div>
                                            <div v-else class="relative">
                                                <input v-model="locationSearchQuery"
                                                       type="text"
                                                       placeholder="Search for location..."
                                                       class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                                                <div class="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                                                    <div v-if="isSearching" class="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                                                    <MapIcon v-else name="search" size="20" class="text-gray-400" />
                                                </div>

                                                <!-- Search Results -->
                                                <div v-if="locationSearchResults && formattedSearchResults.length > 0"
                                                     class="absolute z-10 w-full mt-2 max-h-48 overflow-y-auto bg-white border-2 border-gray-200 rounded-xl shadow-lg">
                                                    <div v-for="result in formattedSearchResults" :key="`${result.type}-${result.id}`"
                                                         @click="selectLocation(result)"
                                                         class="p-4 hover:bg-blue-50 cursor-pointer border-b-2 border-gray-100 last:border-b-0 transition-colors">
                                                        <div class="font-bold text-gray-900">{{ result.name_en || result.name }}</div>
                                                        <div class="text-sm text-gray-600">{{ result.type }}</div>
                                                    </div>
                                                </div>

                                                <!-- No Results -->
                                                <div v-if="!isSearching && locationSearchQuery.length >= 3 && formattedSearchResults.length === 0"
                                                     class="absolute z-10 w-full mt-2 p-4 bg-white border-2 border-gray-200 rounded-xl text-center text-gray-500">
                                                    No results found.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Name -->
                                        <div>
                                            <label class="block text-sm font-bold text-gray-700 mb-2">Name *</label>
                                            <input v-model="editorState.form.name"
                                                   type="text"
                                                   placeholder="Enter place name"
                                                   class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                                            <div v-if="editorState.form.errors.name" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.name[0] }}</div>
                                        </div>

                                        <!-- Description -->
                                        <div>
                                            <label class="block text-sm font-bold text-gray-700 mb-2">Description</label>
                                            <textarea v-model="editorState.form.description"
                                                      rows="3"
                                                      placeholder="Enter description"
                                                      class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors resize-none"></textarea>
                                        </div>

                                        <!-- Address (auto-filled) -->
                                        <div>
                                            <label class="block text-sm font-bold text-gray-700 mb-2">Address *</label>
                                            <input v-model="editorState.form.address"
                                                   type="text"
                                                   placeholder="Address will be auto-filled"
                                                   class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                                            <div v-if="editorState.form.errors.address" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.address[0] }}</div>
                                        </div>

                                        <!-- Coordinates (conditional based on map type or if filled from location) -->
                                        <div v-if="shouldShowCoordinateInputs || (isAdministrationType && (editorState.form.latitude || editorState.form.longitude))" class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-bold text-gray-700 mb-2">
                                                    Latitude
                                                    <span v-if="!isAdministrationType">*</span>
                                                    <span v-if="isAdministrationType && editorState.form.latitude" class="text-green-600 text-xs">(Auto-filled)</span>
                                                </label>
                                                <input v-model="editorState.form.latitude"
                                                       type="number"
                                                       step="any"
                                                       placeholder="e.g. -1.9403"
                                                       :disabled="!allowCoordinateEditing"
                                                       class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors disabled:bg-gray-100" />
                                                <div v-if="editorState.form.errors.latitude" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.latitude[0] }}</div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-bold text-gray-700 mb-2">
                                                    Longitude
                                                    <span v-if="!isAdministrationType">*</span>
                                                    <span v-if="isAdministrationType && editorState.form.longitude" class="text-green-600 text-xs">(Auto-filled)</span>
                                                </label>
                                                <input v-model="editorState.form.longitude"
                                                       type="number"
                                                       step="any"
                                                       placeholder="e.g. 29.8739"
                                                       :disabled="!allowCoordinateEditing"
                                                       class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors disabled:bg-gray-100" />
                                                <div v-if="editorState.form.errors.longitude" class="text-red-600 text-sm mt-1 font-medium">{{ editorState.form.errors.longitude[0] }}</div>
                                            </div>
                                        </div>

                                        <!-- Visibility and Status -->
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-bold text-gray-700 mb-2">Visibility</label>
                                                <select v-model="editorState.form.visibility"
                                                        class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors">
                                                    <option value="private">Private</option>
                                                    <option value="public">Public</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-bold text-gray-700 mb-2">Status</label>
                                                <select v-model="editorState.form.status"
                                                        class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors">
                                                    <option value="active">Active</option>
                                                    <option value="inactive">Inactive</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Geofencing Section (for moving items on tracking maps) -->
                                        <div v-if="isTrackingType && editorState.form.type === 'movingItem' && editorState.isEditing" class="bg-purple-50 p-6 rounded-2xl border-2 border-purple-200">
                                            <div class="flex items-center justify-between mb-4">
                                                <div>
                                                    <h3 class="text-lg font-bold text-purple-900">Geofencing</h3>
                                                    <p class="text-sm text-purple-700">Configure location-based triggers and notifications</p>
                                                </div>
                                                <svg class="w-8 h-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </div>

                                            <!-- API Key Status -->
                                            <div class="mb-4 p-4 bg-blue-50 border-2 border-blue-200 rounded-xl">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-2">
                                                        <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-6m6 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2h6z" />
                                                        </svg>
                                                        <span class="text-sm font-bold text-blue-800">API Key:</span>
                                                    </div>
                                                    <code class="text-xs font-mono text-blue-900 bg-blue-100 px-2 py-1 rounded">{{ getApiKey || 'Not generated' }}</code>
                                                </div>
                                            </div>

                                            <!-- Geofencing Status -->
                                            <div v-if="hasExistingGeofencing" class="mb-4 p-4 bg-green-50 border-2 border-green-200 rounded-xl">
                                                <div class="flex items-center space-x-2">
                                                    <svg class="w-5 h-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                    </svg>
                                                    <span class="text-sm font-bold text-green-800">Geofencing is configured</span>
                                                </div>
                                            </div>

                                            <button @click="openGeofencing" type="button" class="w-full inline-flex items-center justify-center px-6 py-4 bg-purple-600 text-white rounded-2xl font-bold hover:bg-purple-700 transition-colors">
                                                <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                                {{ hasExistingGeofencing ? 'Manage Geofencing Settings' : 'Setup Geofencing' }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Icon Selection Card -->
                                <div class="bg-white p-6 rounded-2xl border-2 border-gray-100">
                                    <h3 class="text-lg font-bold text-gray-900 mb-4">Icon</h3>
                                    <div class="grid grid-cols-4 gap-3">
                                        <button v-for="icon in mapIcons" :key="icon.name"
                                                @click="editorState.form.image = icon.name"
                                                :class="`p-3 rounded-xl border-2 transition-all ${editorState.form.image === icon.name ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`">
                                            <MapIcon :name="icon.name" size="24" class="mx-auto" />
                                        </button>
                                    </div>
                                </div>

                                <!-- Custom Fields Card -->
                                <div v-if="editorState.form.dataItems.length > 0" class="bg-white p-6 rounded-2xl border-2 border-gray-100">
                                    <h3 class="text-lg font-bold text-gray-900 mb-4">Custom Data</h3>
                                    <div class="space-y-4">
                                        <div v-for="(item, index) in editorState.form.dataItems" :key="index">
                                            <label class="block text-sm font-bold text-gray-700 mb-2">{{ item.name }}</label>
                                            <input v-model="item.value"
                                                   type="text"
                                                   class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- Geofencing Modal -->
        <div v-if="showGeofencing" class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="closeGeofencing"></div>

                <!-- Modal panel -->
                <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                    <!-- Header -->
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Geofencing Settings</h3>
                            <p class="text-sm text-gray-600 mt-1">Configure geofencing for {{ selectedItem?.name }}</p>
                        </div>
                        <button @click="closeGeofencing" class="p-2 rounded-xl hover:bg-gray-100 transition-colors">
                            <MapIcon name="close" size="24" class="text-gray-600" />
                        </button>
                    </div>

                    <!-- Form -->
                    <form @submit.prevent="submitGeofencing" class="space-y-6">
                        <!-- API Key Section -->
                        <div class="bg-gray-50 p-6 rounded-2xl border-2 border-gray-100">
                            <h4 class="text-lg font-bold text-gray-900 mb-4">API Configuration</h4>
                            <div class="space-y-4">
                                <!-- Always show API Key -->
                                <div>
                                    <label class="block text-sm font-bold text-gray-700 mb-2">API Key</label>
                                    <div class="p-4 bg-blue-50 border-2 border-blue-200 rounded-xl">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-6m6 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2h6z" />
                                            </svg>
                                            <code class="text-sm font-mono text-blue-900 bg-blue-100 px-2 py-1 rounded">{{ getApiKey || 'No API key generated yet' }}</code>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Enable API Key</label>
                                    <div class="flex space-x-4">
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.apiKey" type="radio" value="yes" class="mr-2" />
                                            <span class="text-sm font-medium">Yes</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.apiKey" type="radio" value="no" class="mr-2" />
                                            <span class="text-sm font-medium">No</span>
                                        </label>
                                    </div>
                                    <div v-if="geofencingForm.errors.apiKey" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.apiKey[0] }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Webhook Section -->
                        <div class="bg-gray-50 p-6 rounded-2xl border-2 border-gray-100">
                            <h4 class="text-lg font-bold text-gray-900 mb-4">Webhook Configuration</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Enable Webhook</label>
                                    <div class="flex space-x-4">
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.webHookStatus" type="radio" value="yes" class="mr-2" />
                                            <span class="text-sm font-medium">Yes</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.webHookStatus" type="radio" value="no" class="mr-2" />
                                            <span class="text-sm font-medium">No</span>
                                        </label>
                                    </div>
                                    <div v-if="geofencingForm.errors.webHookStatus" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.webHookStatus[0] }}</div>
                                </div>

                                <div v-if="geofencingForm.webHookStatus === 'yes'">
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Webhook URL *</label>
                                    <input v-model="geofencingForm.webHookUrl"
                                           type="url"
                                           placeholder="https://example.com/webhook"
                                           class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                                    <div v-if="geofencingForm.errors.webHookUrl" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.webHookUrl[0] }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Geofencing Section -->
                        <div class="bg-gray-50 p-6 rounded-2xl border-2 border-gray-100">
                            <h4 class="text-lg font-bold text-gray-900 mb-4">Geofencing Configuration</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-bold text-gray-700 mb-2">Enable Geofencing</label>
                                    <div class="flex space-x-4">
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.geoFancing" type="radio" value="yes" class="mr-2" />
                                            <span class="text-sm font-medium">Yes</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input v-model="geofencingForm.geoFancing" type="radio" value="no" class="mr-2" />
                                            <span class="text-sm font-medium">No</span>
                                        </label>
                                    </div>
                                    <div v-if="geofencingForm.errors.geoFancing" class="text-red-600 text-sm mt-1 font-medium">{{ geofencingForm.errors.geoFancing[0] }}</div>
                                </div>

                                <!-- Geofencing Data Fields -->
                                <div v-if="geofencingForm.geoFancing === 'yes'" class="space-y-6">
                                    <h5 class="text-md font-bold text-gray-800">Geofencing Parameters</h5>

                                    <!-- Location Search for ID and ID-Type -->
                                    <div class="bg-blue-50 p-4 rounded-xl border-2 border-blue-200">
                                        <h6 class="text-sm font-bold text-blue-900 mb-3">Select Geofencing Location</h6>

                                        <div v-if="selectedGeofencingLocation" class="p-3 bg-white border-2 border-blue-300 rounded-xl mb-3">
                                            <div class="flex justify-between items-center">
                                                <div>
                                                    <div class="font-bold text-blue-900">{{ selectedGeofencingLocation.name_en || selectedGeofencingLocation.name }}</div>
                                                    <div class="text-sm text-blue-700">{{ selectedGeofencingLocation.type }} - ID: {{ selectedGeofencingLocation.id }}</div>
                                                </div>
                                                <button @click="clearGeofencingLocation" class="text-blue-600 hover:text-blue-800 p-2 rounded-xl hover:bg-blue-100 transition-colors">
                                                    <MapIcon name="close" size="16" />
                                                </button>
                                            </div>
                                        </div>

                                        <div v-else class="relative">
                                            <input v-model="geofencingLocationQuery"
                                                   type="text"
                                                   placeholder="Search for province, district, sector, cell, or village..."
                                                   class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                                                <div v-if="isGeofencingSearching" class="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                                                <MapIcon v-else name="search" size="20" class="text-gray-400" />
                                            </div>

                                            <!-- Search Results -->
                                            <div v-if="geofencingLocationResults && geofencingLocationResults.length > 0"
                                                 class="absolute z-10 w-full mt-2 max-h-48 overflow-y-auto bg-white border-2 border-gray-200 rounded-xl shadow-lg">
                                                <div v-for="result in geofencingLocationResults" :key="`${result.type}-${result.id}`"
                                                     @click="selectGeofencingLocation(result)"
                                                     class="p-4 hover:bg-blue-50 cursor-pointer border-b-2 border-gray-100 last:border-b-0 transition-colors">
                                                    <div class="font-bold text-gray-900">{{ result.name_en || result.name }}</div>
                                                    <div class="text-sm text-gray-600">{{ result.type }} - ID: {{ result.id }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Other Geofencing Fields -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div v-for="(field, index) in geofencingForm.geoFancingData" :key="index">
                                            <!-- Skip ID and ID-Type as they're handled by location search -->
                                            <div v-if="field.name !== 'ID' && field.name !== 'ID-Type'">
                                                <label class="block text-sm font-bold text-gray-700 mb-2">{{ field.name }}</label>

                                                <!-- Special handling for Radius field -->
                                                <div v-if="field.name === 'Radius'" class="relative">
                                                    <input v-model="field.value"
                                                           type="number"
                                                           min="1"
                                                           step="1"
                                                           placeholder="10"
                                                           class="w-full px-4 py-3 pr-12 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />
                                                    <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">km</span>
                                                </div>

                                                <!-- Regular text input for other fields -->
                                                <input v-else
                                                       v-model="field.value"
                                                       type="text"
                                                       :placeholder="getFieldPlaceholder(field.name)"
                                                       class="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none transition-colors" />

                                                <div v-if="geofencingForm.errors[`geoFancingData.${index}.value`]" class="text-red-600 text-sm mt-1 font-medium">
                                                    {{ geofencingForm.errors[`geoFancingData.${index}.value`][0] }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-4 pt-6 border-t-2 border-gray-100">
                            <button type="button" @click="closeGeofencing" class="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-2xl font-bold hover:bg-gray-50 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" :disabled="geofencingForm.processing" class="inline-flex items-center px-8 py-3 bg-purple-600 text-white rounded-2xl font-bold hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                <span v-if="geofencingForm.processing" class="mr-2">
                                    <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                {{ geofencingForm.processing ? 'Saving...' : 'Save Geofencing Settings' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
.h-screen-minus-header {
    height: calc(100vh - 65px); /* Adjust 65px to your actual header height */
}
</style>

